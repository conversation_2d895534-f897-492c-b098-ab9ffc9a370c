#%%
import networkx as nx
import matplotlib.pyplot as plt
import numpy as np
import copy
#%%
def visualize_network_state(G):
    """
    可视化给定时间步的网络状态。
    G: 网络图
    step: 时间步，用于图标题
    """
    # 使用 spring_layout 方法来设置节点的位置
    pos = nx.spring_layout(G, seed=42)  # 固定布局种子以保持一致的图形布局

    # 为不同状态设置不同的颜色
    color_map = {'S': 'blue', 'I': 'red', 'R': 'green'}
    node_colors = [color_map[G.nodes[node]['status']] for node in G]

    # 合并显示负载值和负载状态
#     node_labels = {node: f"S:{G.nodes[node]['status']}"
#                    for node in G}

    plt.figure(figsize=(8, 6))
    nx.draw(G, pos, node_color=node_colors, with_labels=False, node_size=700, edge_color='gray', alpha=0.6)
    # 绘制节点标签，设置字体颜色为黑色，字体大小为10
#     nx.draw_networkx_labels(G, pos, labels=node_labels, font_color='black', font_size=10)
    nx.draw_networkx_labels(G, pos, font_color='black', font_size=10)
    plt.show()
    
    
def plot_sir_simulation(susceptible, infected, recovered, style):
    """
    绘制SIR模型的模拟结果。
    
    参数:
    susceptible (list): 易感者数量列表。
    infected (list): 感染者数量列表。
    recovered (list): 恢复者数量列表。
    style (str): 网络类型。
    """
    plt.figure(figsize=(10, 6))
    plt.plot(susceptible, label='Susceptible')
    plt.plot(infected, label='Infected')
    plt.plot(recovered, label='Recovered')
    plt.title(f"SIR Model Simulation in ER network")
    plt.xlabel('Times')
    plt.ylabel('Proportions')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'./{style}.svg',dpi=700,bbox_inches='tight')
    plt.show()
#%%
import networkx as nx

def ensure_connected(G):
    """确保网络是全联通的，如果不是，则通过添加最少的边使其全联通"""
    if not nx.is_connected(G):
        # 获取网络的所有连通分量
        components = list(nx.connected_components(G))
        # 连接所有分量
        while len(components) > 1:
            # 连接第一个和第二个连通分量的任意节点
            G.add_edge(next(iter(components[0])), next(iter(components[1])))
            components = list(nx.connected_components(G))  # 重新获取连通分量
    return G

def create_networks(n, k):
    # 计算ER网络的连接概率
    p_er = k / (n - 1)
    
    # BA网络的每个节点的边数
    m_ba = max(1, k // 2)  # 确保m至少为1
    
    # WS网络的近邻数和重连概率
    k_ws = max(2, k)  # 确保k至少为2，这有助于提高初期的连通性
    p_ws = 0.1  # 重连概率可以根据需要调整

    # 创建网络
    G_er = nx.erdos_renyi_graph(n, p_er)
    G_ba = nx.barabasi_albert_graph(n, m_ba)
    G_ws = nx.watts_strogatz_graph(n, k_ws, p_ws)
    
    # 确保网络是全联通的
    G_er = ensure_connected(G_er)
    G_ba = ensure_connected(G_ba)
    G_ws = ensure_connected(G_ws)
    
    return G_er, G_ba, G_ws


def simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps):
    '''
    模拟网络上的SIR
    G:初始化之后的网络结构
    beta:感染率
    gamma:恢复率
    max_time_steps:模拟最大时间步
    '''
    # 初始化状态
    for node in G:
        G.nodes[node]['status'] = 'S'
    for node in initial_infected_nodes:
        G.nodes[node]['status'] = 'I'
        

    # 初始化计数器
    S_list = [[node for node in G if G.nodes[node]['status'] == 'S']] #存放SIR节点的时间序列数据
    I_list = [[node for node in G if G.nodes[node]['status'] == 'I']]
    R_list = [[node for node in G if G.nodes[node]['status'] == 'R']]
    G_history_list = [copy.deepcopy(G)]
    
    
    # 模拟过程
    for _ in range(max_time_steps):
#         if sum(1 for node in G if G.nodes[node]['status'] == 'S')==0 and sum(1 for node in G if G.nodes[node]['status'] == 'I')==0:
#             #网络中S和I都没有了，说明所有个体都已经感染过了，提前结束循环
#             break
            
        new_infected = []
        new_recovered = []
        
        for node in G:
            if G.nodes[node]['status'] == 'I':
                if np.random.rand() < gamma: #如果节点恢复了，则不再传染
                    new_recovered.append(node)
                else:
                    neighbors = list(G.neighbors(node))
                    for neighbor in neighbors:
                        if G.nodes[neighbor]['status'] == 'S' and np.random.rand() < beta:
                            new_infected.append(neighbor)
                            
        # 更新感染状态
        for node in new_infected:
            G.nodes[node]['status'] = 'I'
        for node in new_recovered:
            G.nodes[node]['status'] = 'R'

        # 更新存储列表
        S_list.append([node for node in G if G.nodes[node]['status'] == 'S'])
        I_list.append([node for node in G if G.nodes[node]['status'] == 'I'])
        R_list.append([node for node in G if G.nodes[node]['status'] == 'R'])
        G_history_list.append(copy.deepcopy(G))
            
    return S_list,I_list,R_list,G_history_list

#%%
# 初始化网络参数
N = 100 # 节点总数
# k = 8 #网络平均度
beta = 0.02  # 感染概率
gamma = 0.01  # 恢复概率
max_time_steps = 600  # 模拟时间步
initial_infected_rate = 0.01 #感染人数比例

# 创建ER随机网络
# Gs = create_networks(N, k) #由上面的函数生成，可以同时生成ER、BA和WS网络。
# G = Gs[1]
G = nx.erdos_renyi_graph(N,0.1,seed=35)
initial_infected_nodes = np.random.choice(list(G.nodes), size=int(N*initial_infected_rate), replace=False)
S_list,I_list,R_list,G_history_list = simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps)
susceptible = [len(i)/N for i in S_list]
infected = [len(i)/N for i in I_list]
recovered = [len(i)/N for i in R_list]
plot_sir_simulation(susceptible, infected, recovered, style='ER_beta0.05')
#%%
import imageio
import os
from PIL import Image, ImageDraw
G_history = G_history_list
def draw_simulation_step(G, step, images_dir):
    plt.figure(figsize=(12, 8))
    # 绘制节点，根据它们的状态使用不同的颜色
    color_map = {'S': 'blue', 'I': 'red', 'R': 'green'}
    # 为每个节点分配颜色
    colors = [color_map[G.nodes[n]['status']] for n in G.nodes()]
    pos = nx.spring_layout(G, seed=42)  # 这里使用spring_layout为了简化，因为你的树结构可能会导致布局看起来不像二叉树
    nx.draw(G, pos, with_labels=False, node_color=colors, node_size=300)
    plt.title(f"Step {step}, Infected: {sum(1 for n in G if G.nodes[n]['status'] == 'I')}")
    plt.savefig(os.path.join(images_dir, f"step_{step:03}.png"))  # 保留三位小数
    plt.close()
    
    
def create_gif(images_dir, gif_path, duration=100):
    images = []
    for step in range(20):
        image_path = os.path.join(images_dir, f"step_{step:03}.png")
        images.append(Image.open(image_path))
    images[0].save(gif_path, save_all=True, append_images=images[1:], duration=duration, loop=0)

for step, G_step in enumerate(G_history):
    draw_simulation_step(G_step, step, images_dir)
#%%
def create_gif(image_folder, output_path, duration=500):
    # 获取图片文件夹中的所有文件名
    images = [img for img in os.listdir(image_folder) if img.endswith(".png")]
    # 按文件名排序
    images.sort()
    
    # 打开图片并存储到列表
    frames = [Image.open(os.path.join(image_folder, img)) for img in images]
    
    # 将第一张图片作为GIF的初始帧
    first_frame = frames[0]
    
    # 保存GIF
    first_frame.save(output_path, format="GIF", append_images=frames[1:], save_all=True, duration=duration, loop=0)
    
image_folder =  "sir_simulation_images"  # 替换为你的图片文件夹路径
output_path = 'output.gif'  # 动图输出路径
create_gif(image_folder, output_path,duration=100)
#%%
import os
import matplotlib.pyplot as plt
import networkx as nx
from PIL import Image, ImageDraw

def draw_simulation_step(G, step, images_dir):
    plt.figure(figsize=(12, 8))
    # 绘制节点，根据它们的状态使用不同的颜色
    color_map = {'S': 'blue', 'I': 'red', 'R': 'green'}
    # 为每个节点分配颜色
    colors = [color_map[G.nodes[n]['status']] for n in G.nodes()]
    pos = nx.spring_layout(G, seed=42)  # 这里使用spring_layout为了简化，因为你的树结构可能会导致布局看起来不像二叉树
    nx.draw(G, pos, with_labels=False, node_color=colors, node_size=300)
    plt.title(f"Step {step}, Infected: {sum(1 for n in G if G.nodes[n]['status'] == 'I')}")
    plt.savefig(os.path.join(images_dir, f"step_{step:03}.png"))  # 保留三位小数
    plt.close()

def create_gif(images_dir, gif_path, duration=100):
    images = []
    for step in range(20):
        image_path = os.path.join(images_dir, f"step_{step:03}.png")
        images.append(Image.open(image_path))
    images[0].save(gif_path, save_all=True, append_images=images[1:], duration=duration, loop=0)

# 示例用法
if __name__ == "__main__":


    # 创建输出目录
    images_dir = 'simulation_images'
    os.makedirs(images_dir, exist_ok=True)

    # 绘制模拟步骤
    for step in range(20):
        draw_simulation_step(G, step, images_dir)

    # 创建GIF动图
    gif_path = os.path.join(images_dir, "sir_simulation.gif")
    create_gif(images_dir, gif_path, duration=100)

#%%
from collections import defaultdict
import pandas as pd 
X = defaultdict(list)
for i in range(500): #只保留S节点多的数据，后面的平滑数据没有意义
    for node in G:
        if node in S_list[i]:
            X[node].append('S')
        elif node in I_list[i]:
            X[node].append('I')
        else:
            X[node].append('R')
df = pd.DataFrame(X)
df = df.T
df.to_csv('SIR_node_status.csv',index=0)
print(df.head())
df_edges = pd.DataFrame(G.edges())
df_edges.to_csv('SIR_edges_index.csv',index=0)
# print(G.edges())
#%%
import numpy as np
import networkx as nx
def run_multiple_simulations(G, beta, gamma, initial_infected_nodes, max_time_steps, num_simulations):
    all_infected = []
    all_recovered = []

    for _ in range(num_simulations):
        S_list, I_list, R_list, G_history_list = simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps)
        infected = [len(i) / N for i in I_list]
        recovered = [len(r) / N for r in R_list]
        all_infected.append(infected)
        all_recovered.append(recovered)

    # 对所有模拟结果取平均
    avg_infected = np.mean(all_infected, axis=0)
    avg_recovered = np.mean(all_recovered, axis=0)
    return avg_infected, avg_recovered

# 主程序
num_simulations = 100  # 定义模拟次数
N = 100  # 假设节点数为1000
max_time_steps = 1000
infected_list = []
rate = [0.01, 0.02, 0.03, 0.04]
for beta in rate:
    G = nx.erdos_renyi_graph(N, 0.1, seed=35)
    gamma = 0.01  # 恢复概率
    avg_infected, _ = run_multiple_simulations(G, beta, gamma, initial_infected_nodes, max_time_steps, num_simulations)
    infected_list.append(avg_infected)

recovered_list = []
for gamma in rate:
    beta = 0.01  # 感染概率
    G = nx.erdos_renyi_graph(N, 0.1, seed=35)
    _, avg_recovered = run_multiple_simulations(G, beta, gamma, initial_infected_nodes, max_time_steps, num_simulations)
    recovered_list.append(avg_recovered)
#%%
plt.figure(figsize=(10, 6))
for i in range(len(recovered_list)):
    plt.plot(recovered_list[i], label=f'gumma = {rate[i]}')
plt.title(f"SIR Model Simulation in ER network")
plt.xlabel('Times')
plt.ylabel('Proportions')
plt.legend()
plt.grid(True)
plt.savefig(f'./beta.svg',dpi=700,bbox_inches='tight')
plt.show()

plt.figure(figsize=(10, 6))
for i in range(len(infected_list)):
    plt.plot(infected_list[i], label=f'beta = {rate[i]}')
plt.title(f"SIR Model Simulation in ER network")
plt.xlabel('Times')
plt.ylabel('Proportions')
plt.legend()
plt.grid(True)
plt.savefig(f'./Gumma.svg',dpi=700,bbox_inches='tight')
plt.show()
#%%
###不同beta和gumma的可视化
##不同beta下I的数量变化
infected_list = []
rate = [0.01,0.02,0.03,0.04]
for beta in rate:
    G = nx.erdos_renyi_graph(N,0.1,seed=35)
    gamma = 0.01  # 恢复概率
    S_list,I_list,R_list,G_history_list = simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps)
    infected = [len(i)/N for i in I_list]
    infected_list.append(infected)
    
recovered_list = []
for gamma in rate:
    beta = 0.01  # 感染概率
    S_list,I_list,R_list,G_history_list = simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps)
    infected = [len(i)/N for i in R_list]
    recovered_list.append(infected)
    
    
    
    
plt.figure(figsize=(10, 6))
for i in range(len(recovered_list)):
    plt.plot(recovered_list[i], label=f'beta = {rate[i]}')
plt.title(f"SIR Model Simulation in ER network")
plt.xlabel('Times')
plt.ylabel('Proportions')
plt.legend()
plt.grid(True)
# plt.savefig(f'./{style}.svg',dpi=700,bbox_inches='tight')
plt.show()
#%%

#%%
# 初始化网络参数
N = 7 #点总数
k = 8 #网络平均度
beta = 0.3  # 感染概率
gamma = 0.1  # 恢复概率
max_time_steps = 100  # 模拟时间步
initial_infected_rate = 0.01 #感染人数比例

# 创建ER随机网络
G = nx.erdos_renyi_graph(N, 0.5) #由上面的函数生成，可以同时生成ER、BA和WS网络。
# initial_infected_nodes = np.random.choice(list(G.nodes), size=int(N*initial_infected_rate), replace=False)
initial_infected_nodes = np.random.choice(list(G.nodes), size=1, replace=False)
S_list,I_list,R_list,G_history_list = simulate_SIR(G, beta, gamma, initial_infected_nodes, max_time_steps)

from collections import defaultdict
import pandas as pd 
X = defaultdict(list)
for i in range(20): #只保留S节点多的数据，后面的平滑数据没有意义
    for node in G:
        if node in S_list[i]:
            X[node].append('S')
        elif node in I_list[i]:
            X[node].append('I')
        else:
            X[node].append('R')
df = pd.DataFrame(X)
df = df.T
df.to_csv('SIR_test_node_status.csv',index=0)
print(df)
df_edges = pd.DataFrame(G.edges())
df_edges.to_csv('SIR_test_edges.csv',index=0)
print(G.edges())
#%%
visualize_network_state(G_history_list[3])
#%%

#%%
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA

# 假设我们有两个样本集，每个样本集有多个点
sample1 = np.random.randn(100, 5)  # 样本集1，有100个点，每个点5个特征
sample2 = np.random.randn(100, 5)  # 样本集2，有100个点，每个点5个特征

# 合并两个样本集
data = np.vstack((sample1, sample2))

# 进行PCA
pca = PCA(n_components=2)
pca_result = pca.fit_transform(data)

# 重新分离样本集
pca_sample1 = pca_result[:100]
pca_sample2 = pca_result[100:]

# 计算两个样本集在PCA空间中每个点的距离
distances = np.sqrt(np.sum((pca_sample1 - pca_sample2)**2, axis=1))

# 可视化结果
plt.scatter(pca_sample1[:, 0], pca_sample1[:, 1], label='Sample 1', alpha=0.5)
plt.scatter(pca_sample2[:, 0], pca_sample2[:, 1], label='Sample 2', alpha=0.5)
for i in range(len(pca_sample1)):
    plt.plot([pca_sample1[i, 0], pca_sample2[i, 0]], [pca_sample1[i, 1], pca_sample2[i, 1]], 'r--', alpha=0.5)

plt.xlabel('PCA Component 1')
plt.ylabel('PCA Component 2')
plt.legend()
plt.title('PCA Biplot with Distance Lines')
plt.show()

# 输出平均距离
mean_distance = np.mean(distances)
print(f'平均距离: {mean_distance}')

#%%

#%%

#%%
