#%%
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.data import Data
from torch_geometric.nn import GCNConv
import numpy as np
import pandas as pd 

# 示例数据
# sir_data = np.array([
#     ['S', 'S', 'I', 'I', 'I', 'R', 'R'],
#     ['S', 'S', 'I', 'I', 'R', 'R', 'R'],
#     ['S', 'S', 'I', 'I', 'R', 'R', 'R'],
#     ['S', 'I', 'I', 'I', 'R', 'R', 'R'],
#     ['S', 'I', 'I', 'I', 'R', 'R', 'R'],
#     ['I', 'I', 'I', 'I', 'R', 'R', 'R'],
#     ['I', 'I', 'I', 'I', 'I', 'I', 'I'],
# ])
df = pd.read_csv('SIR_node_status.csv')  # 如果你的数据是CSV文件

sir_data = df.values.tolist()
# 转换SIR状态为数值特征
def sir_to_features(sir_data):
    feature_map = {'S': 0, 'I': 1, 'R': 2}
    features = np.vectorize(feature_map.get)(sir_data)
    return torch.tensor(features, dtype=torch.float)

features = sir_to_features(sir_data)
num_nodes = features.shape[0]
print(num_nodes)

edge_df = pd.read_csv('SIR_edges_index.csv')
edges = edge_df.values.tolist()
edge_index = torch.tensor(edges).t().contiguous() #真实存在的边列表,[2, 513]
print(edge_index.shape)
#%%
data = Data(x=features[:,0:0+5])
print(data.x.shape)
#%%
edge_df = pd.read_csv('SIR_edges_index.csv')
edges = edge_df.values.tolist()
edge_index = torch.tensor(edges).t().contiguous() #真实存在的边列表,[2, 513]
print(edge_index.shape)
print(edge_index)
#%%
# 创建图数据对象
def comm_edge(edge_index1,edge_index2):
    # 将边列表转换为集合
    edges1_set = set((edge_index1[0, i].item(), edge_index1[1, i].item()) for i in range(edge_index1.size(1)))
    edges2_set = set((edge_index2[0, i].item(), edge_index2[1, i].item()) for i in range(edge_index2.size(1)))
    # 计算交集
    common_edges = edges1_set & edges2_set
    return len(common_edges)/len(edge_index2[1])

def get_top_n_edges(adj_matrix, n):
    num_nodes = adj_matrix.shape[0]
    edges_with_probs = []
    # 构建包含节点对及其概率值的列表
    for i in range(num_nodes):
        for j in range(i + 1, num_nodes):
            edges_with_probs.append(((i, j), adj_matrix[i, j].item()))

    # 按概率值排序并提取前 n 条边
    edges_with_probs.sort(key=lambda x: x[1], reverse=True)
    top_n_edges = edges_with_probs[:n]

    # 转换为需要的格式
    edge_indices = torch.tensor([[i, j] for (i, j), _ in top_n_edges]).t()

    return edge_indices

#%%
class GumbelGraphNetwork(nn.Module):
    def __init__(self, num_nodes, temp=1.0):
        super(GumbelGraphNetwork, self).__init__()
        self.num_nodes = num_nodes
        self.temp = temp
        self.adj_logits = nn.Parameter(torch.randn(num_nodes, num_nodes))

    def forward(self):
        adj_probs = F.gumbel_softmax(self.adj_logits, tau=self.temp, hard=False)
        adj_matrix = adj_probs[:, :, 1] if adj_probs.dim() == 3 else adj_probs
        return adj_matrix

class DynamicsLearner(nn.Module):
    def __init__(self, in_channels, hidden_channels):
        super(DynamicsLearner, self).__init__()
        self.conv1 = GCNConv(in_channels, hidden_channels)
        self.conv2 = GCNConv(hidden_channels, hidden_channels)
        self.fc1 = nn.Linear(hidden_channels, 3)
    
    def forward(self, x, edge_index):
        x = F.relu(self.conv1(x, edge_index))
        x = F.relu(self.conv2(x, edge_index))
        x = F.relu(self.fc1(x))
        return x

class GGN(nn.Module):
    def __init__(self, num_nodes, in_channels, hidden_channels, num_edges, temp=1.0):
        super(GGN, self).__init__()
        self.num_edges = num_edges
        self.generator = GumbelGraphNetwork(num_nodes, temp)
        self.learner = DynamicsLearner(in_channels, hidden_channels)
        
    def get_top_n_edges(self, adj_matrix, n):
        num_nodes = adj_matrix.shape[0]
        edges_with_probs = []
        # 构建包含节点对及其概率值的列表
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edges_with_probs.append(((i, j), adj_matrix[i, j].item()))
        
        # 按概率值排序并提取前 n 条边
        edges_with_probs.sort(key=lambda x: x[1], reverse=True)
        top_n_edges = edges_with_probs[:n]
        
        # 转换为需要的格式
        edge_indices = torch.tensor([[i, j] for (i, j), _ in top_n_edges]).t()
        
        return edge_indices
    
    def forward(self, x):
        adj_matrix = self.generator()
        edge_index_generated = self.get_top_n_edges(adj_matrix, self.num_edges)
#         print(edge_index_generated)
        x = self.learner(x, edge_index_generated)
        return x, adj_matrix

    
# 假设每个节点有三个特征 S, I, R
in_channels = 5
hidden_channels = 16
temp = 0.5
num_nodes = 100
num_edges = 513
model = GGN(num_nodes, in_channels, hidden_channels,num_edges,temp)
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
criterion = torch.nn.CrossEntropyLoss()
    
model.train()
optimizer.zero_grad()

# 预测的边特征
results = []
for i in range(500):
    for epoch in range(200):
        start = i
        end = start + 5
        data = Data(x=features[:,start:end])
        x_node_features, adj_matrix = model(data.x)
    #     label_y = torch.tensor([0, 1, 2, 0,2,1,0], dtype=torch.long)
        label_y = torch.tensor(features[:,end], dtype=torch.long)
        loss = criterion(x_node_features, label_y)#基于模型，学习概率，计算损失
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
    

        if epoch%20==0:
            print(f"Epoch {epoch}: Loss: {loss.item()}")
    reconstructed_edges = get_top_n_edges(adj_matrix,513)
    a = comm_edge(edge_index1 = edge_index,edge_index2=reconstructed_edges)
    results.append(a)
print(results)
#%%
#之后可以根据计算出来的adj_matrix来重构可能存在的边
adj_matrix
#%%
print(max(results))
#%%
def get_top_n_edges(adj_matrix, n):
    num_nodes = adj_matrix.shape[0]
    edges_with_probs = []
    # 构建包含节点对及其概率值的列表
    for i in range(num_nodes):
        for j in range(i + 1, num_nodes):
            edges_with_probs.append(((i, j), adj_matrix[i, j].item()))

    # 按概率值排序并提取前 n 条边
    edges_with_probs.sort(key=lambda x: x[1], reverse=True)
    top_n_edges = edges_with_probs[:n]

    # 转换为需要的格式
    edge_indices = torch.tensor([[i, j] for (i, j), _ in top_n_edges]).t()

    return edge_indices

reconstructed_edges = get_top_n_edges(adj_matrix,513)
print(reconstructed_edges)
#%%
edge_df = pd.read_csv('SIR_edges_index.csv')
edges = edge_df.values.tolist()
edge_index = torch.tensor(edges).t().contiguous() #真实存在的边列表,[2, 513]
print(edge_index.shape)
print(edge_index)

#%%
import torch

# 示例边列表
edge_index1 = reconstructed_edges
edge_index2 = edge_index

def comm_edge(edge_index1,edge_index2):
    # 将边列表转换为集合
    edges1_set = set((edge_index1[0, i].item(), edge_index1[1, i].item()) for i in range(edge_index1.size(1)))
    edges2_set = set((edge_index2[0, i].item(), edge_index2[1, i].item()) for i in range(edge_index2.size(1)))
    # 计算交集
    common_edges = edges1_set & edges2_set
    return len(common_edges),len(common_edges)/{len(edge_index2[1])

# 打印相等的边的个数
print(f"Number of total edges: {len(edge_index2[1])}")
print(f"Number of equal edges: {len(common_edges)}")
print(f"Common edges: {common_edges}")

#%%

#训练过程
def train(model, data, optimizer, criterion, actual_edges, num_epochs=100):
    model.train()
    for epoch in range(num_epochs):
        optimizer.zero_grad()
        
        # 预测的边特征
        edge_features, adj_matrix = model(data.x, data.edge_index)
        
        # 真实的边特征
        actual_edge_index = torch.tensor(actual_edges).t().contiguous()
        labels = torch.sum(data.x[actual_edge_index[0]] * data.x[actual_edge_index[1]], dim=1)
        
        # 计算损失
        loss = criterion(edge_features, labels)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch}, Loss: {loss.item()}')

# 测试过程
def test(model, data, actual_edges):
    model.eval()
    with torch.no_grad():
        edge_features, adj_matrix = model(data.x, data.edge_index)
        adj_probs = torch.sigmoid(edge_features).view(-1)
        probabilities = adj_probs.numpy()
        threshold = 0.5
        edge_index_generated = adj_matrix.nonzero(as_tuple=False).t().contiguous()
        predicted_edges = edge_index_generated[:, probabilities > threshold]

        # 打印预测的边
        predicted_edges = predicted_edges.t().tolist()
        print("Predicted edges:")
        for edge in predicted_edges:
            print(f'{edge[0]} -> {edge[1]}')

        # 提取前5条最有可能的边
        top5_edges = edge_index_generated[:, torch.topk(adj_probs, 5).indices].t().tolist()
        print("Top 5 predicted edges:")
        for edge in top5_edges:
            print(f'{edge[0]} -> {edge[1]}')
#%%


#%%
