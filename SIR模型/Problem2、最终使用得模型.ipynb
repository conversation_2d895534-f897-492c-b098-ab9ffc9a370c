#%%
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
from torch_geometric.utils import from_networkx
import networkx as nx
import numpy as np
import pandas as pd
from sklearn.metrics import confusion_matrix, precision_recall_fscore_support,accuracy_score,precision_score,recall_score,f1_score
import matplotlib.pyplot as plt
#%%

#%%
class GNN_LSTM_Model(nn.Module):
    def __init__(self, num_features, hidden_dim, num_classes, num_nodes, num_time_steps):
        super(GNN_LSTM_Model, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = 1
        self.num_nodes = num_nodes
        self.num_time_steps = num_time_steps
        
        # GNN layers
        self.conv1 = GCNConv(num_features, 128)
        self.conv2 = GCNConv(128, 128)
        self.conv3 = GCNConv(128, 256)
        
        # LSTM layer
        self.lstm = nn.LSTM(input_size=256, hidden_size=hidden_dim, num_layers=self.num_layers, batch_first=True)
        
        # Fully connected layers
        self.fc1 = nn.Linear(hidden_dim, 128)
        self.fc2 = nn.Linear(128, num_classes)
        
    def forward(self, data):
        x_all_time_steps = []
        for t in range(self.num_time_steps):
            x_t, edge_index = data.x[t], data.edge_index[t]
            # GNN layers
            x_t = F.relu(self.conv1(x_t, edge_index))
            x_t = F.relu(self.conv2(x_t, edge_index))
            x_t = F.relu(self.conv3(x_t, edge_index))
            x_all_time_steps.append(x_t)
        
        x_all_time_steps = torch.stack(x_all_time_steps, dim=0)  # Shape: [num_time_steps, num_nodes, 256]
        x_all_time_steps = x_all_time_steps.permute(1, 0, 2).contiguous()  # Shape: [num_nodes, num_time_steps, 256]
#         print(x_all_time_steps.shape)
        # LSTM layer
        h0 = torch.zeros(self.num_layers, self.num_nodes, self.hidden_dim).to(x_all_time_steps.device)
        c0 = torch.zeros(self.num_layers, self.num_nodes, self.hidden_dim).to(x_all_time_steps.device)
        
        x, _ = self.lstm(x_all_time_steps, (h0, c0))  # Output shape: [num_nodes, num_time_steps, hidden_dim] #节点步的表示
        x = x[:, -1, :]  # Get the output from the last time step, shape: [num_nodes, hidden_dim]
        
        # Fully connected layers
        x = F.relu(self.fc1(x))  # Shape: [num_nodes, 128]
        x = self.fc2(x)  # Shape: [num_nodes, num_classes]
        
        return F.log_softmax(x, dim=1)  # Shape: [num_nodes, num_classes]
#%%
def prepare_data(df, window_size):
    # 将SIR状态映射为数字
    status_mapping = {'S': 0, 'I': 1, 'R': 2}
    df = df.replace(status_mapping)  # 将DataFrame中的SIR状态替换为数字
    print(df.shape)

    # 初始化特征和标签列表
    features = []
    labels = []

    # 滑动窗口提取特征和标签
    for start in range(df.shape[1] - window_size):#从0开始，到下表总时间步-步长结束
        end = start + window_size
        # 提取窗口内的数据作为特征
        window_features = df.iloc[:,start:end].values  # Transpose to make features for each node
        features.append(window_features)

        # 下一个时间步的数据作为标签，确保为单个整数数组，并增加一个维度
        window_labels = df.iloc[:,end].values  # 获取单行节点标签
        labels.append(window_labels)  # 直接添加该行为标签数组

    return np.array(features), np.array(labels)



# 假设df是从类似您上传图片的CSV或Excel文件读取的DataFrame
df = pd.read_csv('SIR_node_status.csv')  # 如果你的数据是CSV文件
# 设置窗口大小
window_size = 5
# 调用函数
features, labels = prepare_data(df, window_size)

# 转换为张量
features = torch.tensor(features, dtype=torch.float)
labels = torch.tensor(labels, dtype=torch.long)

# Features shape: (6, 7, 5)特征个数，节点个数，节点特征维度
print("Features shape:", features.shape)
print("Labels shape:", labels.shape)

edge_df = pd.read_csv('SIR_edges_index.csv')
edges = edge_df.values.tolist()
edge_index = torch.tensor(edges).t().contiguous() #边列表
print(edge_index)
#%%
##使用我们的数据训练
#单个数据集演示训练
# 创建示例数据


# 模型参数
num_features = 5
hidden_dim = 64
num_classes = 3
num_nodes = 100
num_time_steps = 5

# 创建模型
model = GNN_LSTM_Model(num_features, hidden_dim, num_classes, num_nodes, num_time_steps)

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)


class Data:
    def __init__(self, x, edge_index):
        self.x = x
        self.edge_index = edge_index
        
#使用前200的时间序列进行实验        
x = features[:200]     
expanded_tensor = edge_index.unsqueeze(0).repeat(200, 1, 1)

# 训练模型
num_epochs = 100      
for epoch in range(num_epochs):
    for i in range(0,int(200*0.8)): #前0.8作为训练集
        optimizer.zero_grad()
        train_data = Data(x=features[i:i+5], edge_index=expanded_tensor[i:i+5])
        out = model(train_data)
        loss = criterion(out, labels[i])
        loss.backward()
        optimizer.step()
    if epoch % 20 == 0:
        print(f"Epoch {epoch}: Loss: {loss.item()}")
        


model.eval()
with torch.no_grad():
    all_true_labels = []
    all_predicted_labels = []
    for i in range(int(200*0.8),195):#后20%作为测试
        test_data = Data(x=features[i:i+5], edge_index=expanded_tensor[i:i+5])
        pred = model(test_data)
        predicted_status = pred.argmax(dim=1) #三类中概率最高的哪一类


        true_labels = labels[i].cpu().numpy() #真实标签的列表
        predicted_labels = predicted_status.cpu().numpy() #预测标签的列表
        
        all_true_labels.extend(true_labels)
        all_predicted_labels.extend(predicted_labels)
    
    # 计算评价指标
    accuracy = accuracy_score(true_labels, predicted_labels)
    precision = precision_score(true_labels, predicted_labels, average='macro',zero_division=1)
    recall = recall_score(true_labels, predicted_labels, average='macro')
    f1 = f1_score(true_labels, predicted_labels, average='macro')

    # 打印评价指标
    print("Accuracy:", accuracy)
    print("Precision:", precision)
    print("Recall:", recall)
    print("F1 Score:", f1)

#%%
#单个数据集演示训练
# 创建示例数据
x = torch.tensor([
    [[0.5, 1.2, 0.3, 0.4, 0.7],
     [0.1, 0.2, 0.3, 0.4, 0.5],
     [1.1, 0.8, 0.7, 0.6, 0.9],
     [0.9, 0.4, 0.2, 0.1, 0.3]],  # 时间步1
    [[0.6, 1.3, 0.4, 0.5, 0.8],
     [0.2, 0.3, 0.4, 0.5, 0.6],
     [1.2, 0.9, 0.8, 0.7, 1.0],
     [1.0, 0.5, 0.3, 0.2, 0.4]],  # 时间步2
    [[0.7, 1.4, 0.5, 0.6, 0.9],
     [0.3, 0.4, 0.5, 0.6, 0.7],
     [1.3, 1.0, 0.9, 0.8, 1.1],
     [1.1, 0.6, 0.4, 0.3, 0.5]]   # 时间步3
], dtype=torch.float)

edge_index = torch.tensor([
    [[0, 1, 2, 3],
     [1, 2, 3, 0]],  # 时间步1
    [[0, 1, 2, 3],
     [1, 2, 3, 0]],  # 时间步2
    [[0, 1, 2, 3],
     [1, 2, 3, 0]]   # 时间步3
], dtype=torch.long)

# 创建一个简化的Data类，用于存储数据
class Data:
    def __init__(self, x, edge_index):
        self.x = x
        self.edge_index = edge_index

data = Data(x=x, edge_index=edge_index)

# 模型参数
num_features = 5
hidden_dim = 64
num_classes = 3
num_nodes = 4
num_time_steps = 3

# 创建模型
model = GNN_LSTM_Model(num_features, hidden_dim, num_classes, num_nodes, num_time_steps)

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
print(data.x)
# 训练模型
num_epochs = 100
for epoch in range(num_epochs):
    model.train()
    optimizer.zero_grad()
    out = model(data)
    y = torch.tensor([0, 1, 2, 0], dtype=torch.long)  # 假设标签
    loss = criterion(out, y)
    loss.backward()
    optimizer.step()
    
    if epoch % 20 == 0:
        print(f'Epoch {epoch}, Loss: {loss.item()}')

# 预测下一个时间步的节点类别
model.eval()
with torch.no_grad():
    out = model(data)
    pred = out.argmax(dim=1)
    print(f'Predicted classes for the next time step: {pred}')


#%%

#%%

#%%

#%%

#%%
class GNN(torch.nn.Module):
    def __init__(self):
        super(GNN, self).__init__()
        self.conv1 = GCNConv(5, 16)  # 输入特征是5，输出特征是16,节点原来是由5维特征表示，现在变成了16维，需要学习的W为5×16，对应输出之后的x为节点数×16
        self.conv2 = GCNConv(16, 3)  # 节点16维变成了3维，需要学习的W为16×3，对应输出之后的x为节点数×3


    def forward(self, data):
        x, edge_index = data.x, data.edge_index #初始x形状维节点数×5
        print('初始数据形状',x.shape)
        x = F.relu(self.conv1(x, edge_index))#x一层卷积后变成了节点数×16
        print('GCN卷积一次之后数据形状',x.shape)
        x = self.conv2(x, edge_index)#x二层卷积后变成了节点数×3
        print('GCN卷积二次之后数据形状',x.shape)
        
        return F.log_softmax(x, dim=1)  # 使用softmax来归一化预测概率
data = Data(x=features[1], edge_index=edge_index,y = labels[1])
# 构建和训练模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = GNN().to(device)
data = data.to(device)
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
criterion = torch.nn.CrossEntropyLoss()
def train():
    model.train()
    optimizer.zero_grad()
    out = model(data)
    data.y = data.y.to(dtype=torch.long)
    loss = criterion(out, data.y)  #这个out可以是log_softmax处理过的x，也可以是未处理的x，criterion都能识别
    loss.backward()
    optimizer.step()
    return loss.item()
for epoch in range(3):#每一次epoch都会更新一次W，同时包括前向传播、计算损失、后向传播
    loss = train()
    print(loss)