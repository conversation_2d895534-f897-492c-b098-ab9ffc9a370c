import pandas as pd
import networkx as nx
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SocialNetworkAnalyzer:
    """社交网络分析器"""

    def __init__(self):
        self.edges_data = None
        self.nodes_data = None
        self.tweet_data = None
        self.graph = None

    def load_data(self):
        """加载数据文件"""
        try:
            print("正在加载数据文件...")
            self.edges_data = pd.read_excel('Edges Data.xlsx')
            self.nodes_data = pd.read_excel('Nodes Data.xlsx')
            self.tweet_data = pd.read_excel('Tweet Contents.xlsx')

            print(f"边数据形状: {self.edges_data.shape}")
            print(f"节点数据形状: {self.nodes_data.shape}")
            print(f"推文数据形状: {self.tweet_data.shape}")

            # 显示数据结构
            print("\n边数据列名:", list(self.edges_data.columns))
            print("节点数据列名:", list(self.nodes_data.columns))
            print("推文数据列名:", list(self.tweet_data.columns))

        except Exception as e:
            print(f"数据加载错误: {e}")
            return False
        return True

    def build_graph(self):
        """构建网络图"""
        try:
            print("\n正在构建网络图...")
            self.graph = nx.DiGraph()  # 有向图

            # 添加节点
            if self.nodes_data is not None:
                for _, row in self.nodes_data.iterrows():
                    node_id = row.iloc[0]  # 假设第一列是用户ID
                    self.graph.add_node(node_id, **row.to_dict())

            # 添加边
            if self.edges_data is not None:
                for _, row in self.edges_data.iterrows():
                    source = row.iloc[0]  # 假设第一列是源节点
                    target = row.iloc[1]  # 假设第二列是目标节点
                    self.graph.add_edge(source, target, **row.to_dict())

            print(f"网络图构建完成:")
            print(f"节点数: {self.graph.number_of_nodes()}")
            print(f"边数: {self.graph.number_of_edges()}")

        except Exception as e:
            print(f"图构建错误: {e}")
            return False
        return True

    def analyze_degree_distribution(self):
        """分析度分布"""
        if self.graph is None:
            print("请先构建网络图")
            return None

        print("\n=== 度分布分析 ===")

        # 计算各种度数
        in_degrees = dict(self.graph.in_degree())
        out_degrees = dict(self.graph.out_degree())
        total_degrees = dict(self.graph.degree())

        # 统计信息
        in_degree_values = list(in_degrees.values())
        out_degree_values = list(out_degrees.values())
        total_degree_values = list(total_degrees.values())

        degree_stats = {
            '入度': {
                '平均值': np.mean(in_degree_values),
                '中位数': np.median(in_degree_values),
                '标准差': np.std(in_degree_values),
                '最大值': np.max(in_degree_values),
                '最小值': np.min(in_degree_values)
            },
            '出度': {
                '平均值': np.mean(out_degree_values),
                '中位数': np.median(out_degree_values),
                '标准差': np.std(out_degree_values),
                '最大值': np.max(out_degree_values),
                '最小值': np.min(out_degree_values)
            },
            '总度数': {
                '平均值': np.mean(total_degree_values),
                '中位数': np.median(total_degree_values),
                '标准差': np.std(total_degree_values),
                '最大值': np.max(total_degree_values),
                '最小值': np.min(total_degree_values)
            }
        }

        # 打印统计信息
        for degree_type, stats in degree_stats.items():
            print(f"\n{degree_type}统计:")
            for stat_name, value in stats.items():
                print(f"  {stat_name}: {value:.2f}")

        return {
            'in_degrees': in_degrees,
            'out_degrees': out_degrees,
            'total_degrees': total_degrees,
            'stats': degree_stats
        }

    def analyze_clustering_coefficient(self):
        """分析集聚系数"""
        if self.graph is None:
            print("请先构建网络图")
            return None

        print("\n=== 集聚系数分析 ===")

        # 转换为无向图计算集聚系数
        undirected_graph = self.graph.to_undirected()

        # 计算局部集聚系数
        local_clustering = nx.clustering(undirected_graph)

        # 计算全局集聚系数
        global_clustering = nx.average_clustering(undirected_graph)

        # 计算传递性（另一种全局集聚系数的度量）
        transitivity = nx.transitivity(undirected_graph)

        # 统计信息
        clustering_values = list(local_clustering.values())

        clustering_stats = {
            '局部集聚系数': {
                '平均值': np.mean(clustering_values),
                '中位数': np.median(clustering_values),
                '标准差': np.std(clustering_values),
                '最大值': np.max(clustering_values),
                '最小值': np.min(clustering_values)
            },
            '全局集聚系数': global_clustering,
            '传递性': transitivity
        }

        print(f"全局集聚系数: {global_clustering:.4f}")
        print(f"传递性: {transitivity:.4f}")
        print(f"局部集聚系数平均值: {np.mean(clustering_values):.4f}")
        print(f"局部集聚系数标准差: {np.std(clustering_values):.4f}")

        return {
            'local_clustering': local_clustering,
            'global_clustering': global_clustering,
            'transitivity': transitivity,
            'stats': clustering_stats
        }

    def plot_degree_distribution(self, degree_data):
        """绘制度分布图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('度分布分析', fontsize=16, fontweight='bold')

        # 入度分布
        in_degrees = list(degree_data['in_degrees'].values())
        axes[0, 0].hist(in_degrees, bins=50, alpha=0.7, color='blue', edgecolor='black')
        axes[0, 0].set_title('入度分布')
        axes[0, 0].set_xlabel('入度')
        axes[0, 0].set_ylabel('频数')
        axes[0, 0].grid(True, alpha=0.3)

        # 出度分布
        out_degrees = list(degree_data['out_degrees'].values())
        axes[0, 1].hist(out_degrees, bins=50, alpha=0.7, color='red', edgecolor='black')
        axes[0, 1].set_title('出度分布')
        axes[0, 1].set_xlabel('出度')
        axes[0, 1].set_ylabel('频数')
        axes[0, 1].grid(True, alpha=0.3)

        # 总度数分布
        total_degrees = list(degree_data['total_degrees'].values())
        axes[0, 2].hist(total_degrees, bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[0, 2].set_title('总度数分布')
        axes[0, 2].set_xlabel('总度数')
        axes[0, 2].set_ylabel('频数')
        axes[0, 2].grid(True, alpha=0.3)

        # 对数尺度的度分布
        axes[1, 0].hist(in_degrees, bins=50, alpha=0.7, color='blue', edgecolor='black')
        axes[1, 0].set_yscale('log')
        axes[1, 0].set_title('入度分布 (对数尺度)')
        axes[1, 0].set_xlabel('入度')
        axes[1, 0].set_ylabel('频数 (对数)')
        axes[1, 0].grid(True, alpha=0.3)

        axes[1, 1].hist(out_degrees, bins=50, alpha=0.7, color='red', edgecolor='black')
        axes[1, 1].set_yscale('log')
        axes[1, 1].set_title('出度分布 (对数尺度)')
        axes[1, 1].set_xlabel('出度')
        axes[1, 1].set_ylabel('频数 (对数)')
        axes[1, 1].grid(True, alpha=0.3)

        axes[1, 2].hist(total_degrees, bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[1, 2].set_yscale('log')
        axes[1, 2].set_title('总度数分布 (对数尺度)')
        axes[1, 2].set_xlabel('总度数')
        axes[1, 2].set_ylabel('频数 (对数)')
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('度分布分析.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("度分布图已保存为: 度分布分析.png")

    def plot_clustering_distribution(self, clustering_data):
        """绘制集聚系数分布图"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('集聚系数分析', fontsize=16, fontweight='bold')

        clustering_values = list(clustering_data['local_clustering'].values())

        # 集聚系数分布直方图
        axes[0].hist(clustering_values, bins=50, alpha=0.7, color='purple', edgecolor='black')
        axes[0].set_title('局部集聚系数分布')
        axes[0].set_xlabel('集聚系数')
        axes[0].set_ylabel('频数')
        axes[0].grid(True, alpha=0.3)

        # 添加统计信息
        mean_clustering = np.mean(clustering_values)
        axes[0].axvline(mean_clustering, color='red', linestyle='--',
                       label=f'平均值: {mean_clustering:.3f}')
        axes[0].axvline(clustering_data['global_clustering'], color='orange', linestyle='--',
                       label=f'全局集聚系数: {clustering_data["global_clustering"]:.3f}')
        axes[0].legend()

        # 集聚系数箱线图
        axes[1].boxplot(clustering_values, vert=True)
        axes[1].set_title('局部集聚系数箱线图')
        axes[1].set_ylabel('集聚系数')
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('集聚系数分析.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("集聚系数图已保存为: 集聚系数分析.png")

    def analyze_network_properties(self):
        """分析网络基本属性"""
        if self.graph is None:
            print("请先构建网络图")
            return None

        print("\n=== 网络基本属性分析 ===")

        # 基本统计
        num_nodes = self.graph.number_of_nodes()
        num_edges = self.graph.number_of_edges()

        # 密度
        density = nx.density(self.graph)

        # 连通性分析
        if nx.is_directed(self.graph):
            # 有向图的连通性
            weakly_connected = nx.is_weakly_connected(self.graph)
            strongly_connected = nx.is_strongly_connected(self.graph)
            num_weakly_cc = nx.number_weakly_connected_components(self.graph)
            num_strongly_cc = nx.number_strongly_connected_components(self.graph)

            print(f"弱连通: {weakly_connected}")
            print(f"强连通: {strongly_connected}")
            print(f"弱连通分量数: {num_weakly_cc}")
            print(f"强连通分量数: {num_strongly_cc}")
        else:
            # 无向图的连通性
            connected = nx.is_connected(self.graph)
            num_cc = nx.number_connected_components(self.graph)

            print(f"连通: {connected}")
            print(f"连通分量数: {num_cc}")

        print(f"节点数: {num_nodes}")
        print(f"边数: {num_edges}")
        print(f"网络密度: {density:.6f}")

        # 对于大型网络，跳过耗时的路径长度计算
        print("注意: 由于网络规模较大，跳过平均路径长度和直径计算")
        avg_path_length = None
        diameter = None

        return {
            'num_nodes': num_nodes,
            'num_edges': num_edges,
            'density': density,
            'avg_path_length': avg_path_length,
            'diameter': diameter
        }

    def generate_comprehensive_report(self, degree_data, clustering_data, network_props):
        """生成综合分析报告"""
        print("\n" + "="*60)
        print("           复杂网络结构特征分析报告")
        print("="*60)

        print("\n【网络基本信息】")
        print(f"节点数量: {network_props['num_nodes']:,}")
        print(f"边数量: {network_props['num_edges']:,}")
        print(f"网络密度: {network_props['density']:.6f}")
        if network_props['avg_path_length']:
            print(f"平均路径长度: {network_props['avg_path_length']:.4f}")
        if network_props['diameter']:
            print(f"网络直径: {network_props['diameter']}")

        print("\n【度分布特征】")
        for degree_type, stats in degree_data['stats'].items():
            print(f"\n{degree_type}:")
            print(f"  平均值: {stats['平均值']:.2f}")
            print(f"  标准差: {stats['标准差']:.2f}")
            print(f"  最大值: {stats['最大值']}")
            print(f"  最小值: {stats['最小值']}")

        print("\n【集聚系数特征】")
        print(f"全局集聚系数: {clustering_data['global_clustering']:.4f}")
        print(f"传递性: {clustering_data['transitivity']:.4f}")
        print(f"局部集聚系数平均值: {clustering_data['stats']['局部集聚系数']['平均值']:.4f}")
        print(f"局部集聚系数标准差: {clustering_data['stats']['局部集聚系数']['标准差']:.4f}")

        print("\n【结构特征解释】")
        self._interpret_network_structure(degree_data, clustering_data, network_props)

    def _interpret_network_structure(self, degree_data, clustering_data, network_props):
        """解释网络结构特征"""
        print("\n1. 度分布特征:")

        # 分析度分布的偏斜性
        in_degrees = list(degree_data['in_degrees'].values())
        out_degrees = list(degree_data['out_degrees'].values())

        in_mean = np.mean(in_degrees)
        in_std = np.std(in_degrees)
        out_mean = np.mean(out_degrees)
        out_std = np.std(out_degrees)

        if in_std / in_mean > 1:
            print("   - 入度分布呈现高度异质性，存在少数高入度节点（意见领袖）")
        else:
            print("   - 入度分布相对均匀，用户影响力较为平衡")

        if out_std / out_mean > 1:
            print("   - 出度分布呈现高度异质性，存在少数高活跃用户")
        else:
            print("   - 出度分布相对均匀，用户活跃度较为平衡")

        print("\n2. 集聚特征:")
        global_clustering = clustering_data['global_clustering']

        if global_clustering > 0.3:
            print("   - 网络具有较强的集聚特性，用户倾向于形成紧密的社群")
        elif global_clustering > 0.1:
            print("   - 网络具有中等程度的集聚特性，存在一定的社群结构")
        else:
            print("   - 网络集聚程度较低，用户连接相对分散")

        print("\n3. 小世界特征:")
        if network_props['avg_path_length']:
            # 简单的小世界判断
            expected_random_clustering = network_props['density']
            clustering_ratio = global_clustering / expected_random_clustering if expected_random_clustering > 0 else float('inf')

            if clustering_ratio > 1 and network_props['avg_path_length'] < np.log(network_props['num_nodes']):
                print("   - 网络可能具有小世界特征：高集聚性 + 短平均路径长度")
            else:
                print("   - 网络不具备典型的小世界特征")

        print("\n4. 网络类型推断:")
        density = network_props['density']

        if density > 0.1:
            print("   - 高密度网络，用户之间联系紧密")
        elif density > 0.01:
            print("   - 中密度网络，存在适度的用户互动")
        else:
            print("   - 低密度网络，用户连接稀疏，可能存在明显的社群分化")


def main():
    """主函数：执行完整的网络分析"""
    print("开始社交网络复杂网络分析...")
    print("="*60)

    # 创建分析器实例
    analyzer = SocialNetworkAnalyzer()

    # 1. 加载数据
    if not analyzer.load_data():
        print("数据加载失败，程序退出")
        return

    # 2. 构建网络图
    if not analyzer.build_graph():
        print("网络图构建失败，程序退出")
        return

    # 3. 度分布分析
    print("\n正在进行度分布分析...")
    degree_data = analyzer.analyze_degree_distribution()
    if degree_data:
        analyzer.plot_degree_distribution(degree_data)

    # 4. 集聚系数分析
    print("\n正在进行集聚系数分析...")
    clustering_data = analyzer.analyze_clustering_coefficient()
    if clustering_data:
        analyzer.plot_clustering_distribution(clustering_data)

    # 5. 网络属性分析
    print("\n正在进行网络属性分析...")
    network_props = analyzer.analyze_network_properties()

    # 6. 生成综合报告
    if degree_data and clustering_data and network_props:
        analyzer.generate_comprehensive_report(degree_data, clustering_data, network_props)

    print("\n分析完成！")
    print("生成的文件:")
    print("- 度分布分析.png")
    print("- 集聚系数分析.png")


if __name__ == "__main__":
    main()