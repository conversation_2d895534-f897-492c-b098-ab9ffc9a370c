import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_data_comparison():
    """对比分析三个数据集的特征"""
    
    print("正在加载数据文件进行对比分析...")
    
    # 加载数据
    edges_data = pd.read_excel('Edges Data.xlsx')
    nodes_data = pd.read_excel('Nodes Data.xlsx')
    tweet_data = pd.read_excel('Tweet Contents.xlsx')
    
    print("=== 数据集基本信息对比 ===")
    print(f"边数据: {edges_data.shape[0]:,} 条记录, {edges_data.shape[1]} 个字段")
    print(f"节点数据: {nodes_data.shape[0]:,} 条记录, {nodes_data.shape[1]} 个字段")
    print(f"推文数据: {tweet_data.shape[0]:,} 条记录, {tweet_data.shape[1]} 个字段")
    
    # 分析边数据
    print("\n=== 边数据分析 ===")
    print("字段名:", list(edges_data.columns))
    
    # 用户交互统计
    source_users = edges_data.iloc[:, 0].nunique()
    target_users = edges_data.iloc[:, 1].nunique()
    total_interactions = len(edges_data)
    
    print(f"源用户数: {source_users:,}")
    print(f"目标用户数: {target_users:,}")
    print(f"总交互数: {total_interactions:,}")
    
    # 分析节点数据
    print("\n=== 节点数据分析 ===")
    print("字段名:", list(nodes_data.columns))
    
    if 'Followers' in nodes_data.columns:
        followers_stats = nodes_data['Followers'].describe()
        print("粉丝数统计:")
        print(f"  平均值: {followers_stats['mean']:.0f}")
        print(f"  中位数: {followers_stats['50%']:.0f}")
        print(f"  最大值: {followers_stats['max']:.0f}")
        print(f"  最小值: {followers_stats['min']:.0f}")
    
    if 'Followed' in nodes_data.columns:
        following_stats = nodes_data['Followed'].describe()
        print("关注数统计:")
        print(f"  平均值: {following_stats['mean']:.0f}")
        print(f"  中位数: {following_stats['50%']:.0f}")
        print(f"  最大值: {following_stats['max']:.0f}")
        print(f"  最小值: {following_stats['min']:.0f}")
    
    if 'Verified' in nodes_data.columns:
        verified_count = nodes_data['Verified'].sum()
        verified_rate = verified_count / len(nodes_data) * 100
        print(f"认证用户数: {verified_count:,} ({verified_rate:.2f}%)")
    
    # 分析推文数据
    print("\n=== 推文数据分析 ===")
    print("字段名:", list(tweet_data.columns))
    
    if 'Tweet' in tweet_data.columns:
        tweet_lengths = tweet_data['Tweet'].astype(str).str.len()
        print("推文长度统计:")
        print(f"  平均长度: {tweet_lengths.mean():.1f} 字符")
        print(f"  中位数长度: {tweet_lengths.median():.1f} 字符")
        print(f"  最长推文: {tweet_lengths.max()} 字符")
        print(f"  最短推文: {tweet_lengths.min()} 字符")
    
    # 创建对比可视化
    create_comparison_plots(edges_data, nodes_data, tweet_data)
    
    return edges_data, nodes_data, tweet_data

def create_comparison_plots(edges_data, nodes_data, tweet_data):
    """创建数据对比可视化图表"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('三个数据集特征对比分析', fontsize=16, fontweight='bold')
    
    # 1. 数据规模对比
    datasets = ['边数据', '节点数据', '推文数据']
    record_counts = [len(edges_data), len(nodes_data), len(tweet_data)]
    
    axes[0, 0].bar(datasets, record_counts, color=['blue', 'green', 'red'], alpha=0.7)
    axes[0, 0].set_title('数据集记录数对比')
    axes[0, 0].set_ylabel('记录数')
    for i, v in enumerate(record_counts):
        axes[0, 0].text(i, v + max(record_counts)*0.01, f'{v:,}', ha='center', va='bottom')
    
    # 2. 用户活跃度分析（基于边数据）
    user_activity = edges_data.iloc[:, 0].value_counts()
    axes[0, 1].hist(user_activity.values, bins=50, alpha=0.7, color='blue', edgecolor='black')
    axes[0, 1].set_title('用户活跃度分布（发出交互数）')
    axes[0, 1].set_xlabel('交互次数')
    axes[0, 1].set_ylabel('用户数')
    axes[0, 1].set_yscale('log')
    
    # 3. 用户受欢迎度分析（基于边数据）
    user_popularity = edges_data.iloc[:, 1].value_counts()
    axes[0, 2].hist(user_popularity.values, bins=50, alpha=0.7, color='green', edgecolor='black')
    axes[0, 2].set_title('用户受欢迎度分布（接收交互数）')
    axes[0, 2].set_xlabel('被交互次数')
    axes[0, 2].set_ylabel('用户数')
    axes[0, 2].set_yscale('log')
    
    # 4. 粉丝数分布（如果有数据）
    if 'Followers' in nodes_data.columns:
        followers = nodes_data['Followers']
        axes[1, 0].hist(followers[followers > 0], bins=50, alpha=0.7, color='purple', edgecolor='black')
        axes[1, 0].set_title('用户粉丝数分布')
        axes[1, 0].set_xlabel('粉丝数')
        axes[1, 0].set_ylabel('用户数')
        axes[1, 0].set_xscale('log')
        axes[1, 0].set_yscale('log')
    else:
        axes[1, 0].text(0.5, 0.5, '无粉丝数据', ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 0].set_title('粉丝数分布（无数据）')
    
    # 5. 关注数分布（如果有数据）
    if 'Followed' in nodes_data.columns:
        following = nodes_data['Followed']
        axes[1, 1].hist(following[following > 0], bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 1].set_title('用户关注数分布')
        axes[1, 1].set_xlabel('关注数')
        axes[1, 1].set_ylabel('用户数')
        axes[1, 1].set_xscale('log')
        axes[1, 1].set_yscale('log')
    else:
        axes[1, 1].text(0.5, 0.5, '无关注数据', ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('关注数分布（无数据）')
    
    # 6. 推文长度分布
    if 'Tweet' in tweet_data.columns:
        tweet_lengths = tweet_data['Tweet'].astype(str).str.len()
        axes[1, 2].hist(tweet_lengths, bins=50, alpha=0.7, color='red', edgecolor='black')
        axes[1, 2].set_title('推文长度分布')
        axes[1, 2].set_xlabel('字符数')
        axes[1, 2].set_ylabel('推文数')
    else:
        axes[1, 2].text(0.5, 0.5, '无推文数据', ha='center', va='center', transform=axes[1, 2].transAxes)
        axes[1, 2].set_title('推文长度分布（无数据）')
    
    plt.tight_layout()
    plt.savefig('数据集对比分析.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("数据对比图已保存为: 数据集对比分析.png")

def generate_summary_report(edges_data, nodes_data, tweet_data):
    """生成数据对比总结报告"""
    
    print("\n" + "="*60)
    print("           数据集对比分析总结报告")
    print("="*60)
    
    print("\n【数据完整性分析】")
    print(f"边数据记录数: {len(edges_data):,}")
    print(f"推文数据记录数: {len(tweet_data):,}")
    print(f"记录数一致性: {'✓' if len(edges_data) == len(tweet_data) else '✗'}")
    
    unique_users_in_edges = set(edges_data.iloc[:, 0]) | set(edges_data.iloc[:, 1])
    print(f"边数据中涉及的用户数: {len(unique_users_in_edges):,}")
    print(f"节点数据中的用户数: {len(nodes_data):,}")
    
    coverage = len(nodes_data) / len(unique_users_in_edges) * 100
    print(f"节点数据覆盖率: {coverage:.1f}%")
    
    print("\n【数据质量特征】")
    
    # 边数据质量
    duplicate_edges = edges_data.duplicated().sum()
    print(f"边数据重复记录: {duplicate_edges:,} ({duplicate_edges/len(edges_data)*100:.2f}%)")
    
    # 节点数据质量
    if 'Followers' in nodes_data.columns:
        zero_followers = (nodes_data['Followers'] == 0).sum()
        print(f"零粉丝用户: {zero_followers:,} ({zero_followers/len(nodes_data)*100:.2f}%)")
    
    if 'Verified' in nodes_data.columns:
        verified_users = nodes_data['Verified'].sum()
        print(f"认证用户比例: {verified_users/len(nodes_data)*100:.2f}%")
    
    # 推文数据质量
    if 'Tweet' in tweet_data.columns:
        empty_tweets = tweet_data['Tweet'].isna().sum()
        print(f"空推文记录: {empty_tweets:,} ({empty_tweets/len(tweet_data)*100:.2f}%)")
    
    print("\n【数据集特征对比】")
    print("1. 边数据特征:")
    print("   - 记录用户间的交互关系")
    print("   - 包含时间信息，支持时序分析")
    print("   - 是构建网络图的基础数据")
    
    print("\n2. 节点数据特征:")
    print("   - 提供用户的基本属性信息")
    print("   - 包含社交影响力指标（粉丝数、关注数）")
    print("   - 支持用户分类和影响力分析")
    
    print("\n3. 推文数据特征:")
    print("   - 包含具体的文本内容")
    print("   - 支持情感分析和主题挖掘")
    print("   - 与边数据一一对应，支持内容-网络联合分析")

if __name__ == "__main__":
    print("开始数据集对比分析...")
    edges_data, nodes_data, tweet_data = analyze_data_comparison()
    generate_summary_report(edges_data, nodes_data, tweet_data)
    print("\n对比分析完成！")
    print("生成的文件: 数据集对比分析.png")
